import asyncio
import re
from collections import deque
from enum import Enum
from typing import List, Tuple

class BufferType(Enum):
    SENSITIVE = "sensitive"
    CORNER = "corner"
    CONTENT = "content"

class Buffer:
    def __init__(self, content: str, type_: BufferType):
        self.content = content
        self.type_ = type_
    
    def __repr__(self):
        return f"Buffer(content='{self.content}', type_={self.type_.value})"

class TrieNode:
    def __init__(self):
        self.children = {}
        self.fail = None
        self.output = []

class ACAutomaton:
    def __init__(self):
        self.root = TrieNode()

    def add_word(self, pattern):
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.output.append(pattern)

    def build_failure_pointers(self):
        queue = deque()
        
        for char, node in self.root.children.items():
            node.fail = self.root
            queue.append(node)

        while queue:
            current_node = queue.popleft()
            
            for char, next_node in current_node.children.items():
                fail_node = current_node.fail
                while fail_node is not None and char not in fail_node.children:
                    fail_node = fail_node.fail
                if fail_node is None:
                    next_node.fail = self.root
                else:
                    next_node.fail = fail_node.children[char]
                    next_node.output.extend(next_node.fail.output)
                queue.append(next_node)

class FixedQABuffer:
    corner_keyword_pattern = re.compile(r'\[\s*citation\s*:\s*(\d+(?:\s*,\s*\d+)*)\s*\]')
    
    def __init__(self, keywords: List[str] = None):
        self.buffer_deque = deque()
        self.content = ""
        self.thinking_flag = False
        self.keywords = set(keywords) if keywords else set()
        
        self.ac = ACAutomaton()
        self.citation_content = {"[", "citation", ":"}
        self.citation_end = {"]"}
        for keyword in self.keywords | self.citation_content | self.citation_end:
            self.ac.add_word(keyword)
        self.ac.build_failure_pointers()

    async def generator(self, stream):
        node = self.ac.root

        async for char in stream:
            print(f"处理字符: '{char}', 当前缓冲区: '{''.join(self.buffer_deque)}'")
            
            # AC自动机状态转移
            while node is not None and char not in node.children:
                node = node.fail

            if node is None:
                node = self.ac.root
                # 重新尝试从root开始匹配当前字符
                if char in node.children:
                    node = node.children[char]
                    self.buffer_deque.append(char)
                    # 检查是否有匹配输出，如果有则继续到下面的匹配处理逻辑
                    if not node.output:
                        continue
                else:
                    # 当前字符无法匹配AC自动机，但可能是citation模式的一部分
                    # 先添加到缓冲区，然后检查是否能形成完整的citation
                    self.buffer_deque.append(char)
                    buffer_text = "".join(self.buffer_deque)
                    print(f"  无法匹配字符 '{char}', 缓冲区: '{buffer_text}'")

                    # 如果缓冲区有潜在的citation模式，继续积累
                    if self._has_potential_citation(buffer_text):
                        print(f"  检测到潜在citation，继续积累")
                        continue

                    # 如果没有潜在的citation模式，输出缓冲区内容
                    print(f"  没有潜在citation，清空缓冲区")
                    while self.buffer_deque:
                        char_to_output = self.buffer_deque.popleft()
                        print(f"  输出缓冲区字符: '{char_to_output}'")
                        yield Buffer(content=char_to_output, type_=BufferType.CONTENT)
                    continue
            else:
                node = node.children.get(char)

            # 添加字符到缓冲区
            self.buffer_deque.append(char)
            buffer_text = "".join(self.buffer_deque)
            print(f"  缓冲区内容: '{buffer_text}'")

            # 检查敏感词匹配
            if node and node.output:
                sensitive_words = [word for word in node.output if word not in {"[", "citation", ":", "]"}]
                if sensitive_words:
                    yield Buffer(content=",".join(sensitive_words), type_=BufferType.SENSITIVE)
                    return

            # 检查citation模式匹配
            if matches := self.find_corner_marks(buffer_text):
                print(f"  找到citation匹配: {matches}")
                # 处理第一个匹配（通常只有一个完整匹配）
                start, end, refer_ids = matches[0]

                # 输出citation之前的内容
                if start > 0:
                    prefix = buffer_text[:start]
                    for c in prefix:
                        print(f"  输出前缀字符: '{c}'")
                        yield Buffer(content=c, type_=BufferType.CONTENT)

                # 输出citation引用ID
                for ref_id in refer_ids:
                    print(f"  输出引用ID: '{ref_id}'")
                    yield Buffer(content=ref_id, type_=BufferType.CORNER)

                # 清理已处理的内容，保留未处理的部分
                remaining = buffer_text[end:]
                self.buffer_deque.clear()
                for c in remaining:
                    self.buffer_deque.append(c)

                node = self.ac.root
                continue

            # 防止缓冲区无限增长
            if len(self.buffer_deque) > 20 and not self._has_potential_citation(buffer_text):
                char_to_output = self.buffer_deque.popleft()
                print(f"  防止增长，输出: '{char_to_output}'")
                yield Buffer(content=char_to_output, type_=BufferType.CONTENT)

        # 处理剩余缓冲区内容
        while self.buffer_deque:
            char_to_output = self.buffer_deque.popleft()
            print(f"最终输出缓冲区字符: '{char_to_output}'")
            yield Buffer(content=char_to_output, type_=BufferType.CONTENT)

    def _has_potential_citation(self, text: str) -> bool:
        """检查文本是否包含潜在的citation模式开始"""
        return '[' in text and ('citation' in text or 'c' in text)

    @classmethod
    def find_corner_marks(cls, text: str) -> List[Tuple[int, int, List[str]]]:
        """查找citation标记"""
        if not text:
            return []
        
        matches = []
        for match in cls.corner_keyword_pattern.finditer(text):
            start, end = match.span()
            refs = match.group(1)
            ref_ids = [num.strip() for num in refs.split(',') if num.strip().isdigit()]
            matches.append((start, end, ref_ids))
        
        return matches

async def test_citation_matching():
    """测试citation匹配"""
    test_text = "[citation:1]"
    
    async def char_stream():
        for char in test_text:
            yield char
    
    buffer = FixedQABuffer()
    
    print(f"测试文本: {test_text}")
    print("=" * 50)
    
    results = []
    async for result in buffer.generator(char_stream()):
        results.append(result)
        print(f"输出: {result}")
    
    print("=" * 50)
    print("最终结果:", "".join([r.content for r in results]))
    print("引用ID:", [r.content for r in results if r.type_ == BufferType.CORNER])

if __name__ == "__main__":
    asyncio.run(test_citation_matching())
