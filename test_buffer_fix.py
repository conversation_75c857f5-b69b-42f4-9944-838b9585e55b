import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from controller.chat.buffer import QABuffer, BufferType

async def test_citation_stream():
    """测试citation匹配的字符流"""
    test_text = "这是一个测试[citation:1]文本"
    
    async def char_stream():
        for char in test_text:
            yield char, "normal"
    
    buffer = QABuffer(stream=char_stream(), keywords=[], reference=[1])
    
    print("测试文本:", test_text)
    print("输出结果:")
    
    async for result in buffer.generator():
        print(f"  {result.type_.value}: '{result.content}'")

async def test_bracket_only():
    """测试单独的'['字符"""
    test_text = "这是一个[测试文本"
    
    async def char_stream():
        for char in test_text:
            yield char, "normal"
    
    buffer = QABuffer(stream=char_stream(), keywords=[], reference=[1])
    
    print("\n测试文本:", test_text)
    print("输出结果:")
    
    async for result in buffer.generator():
        print(f"  {result.type_.value}: '{result.content}'")

async def test_incomplete_citation():
    """测试不完整的citation"""
    test_text = "这是一个[citation测试"
    
    async def char_stream():
        for char in test_text:
            yield char, "normal"
    
    buffer = QABuffer(stream=char_stream(), keywords=[], reference=[1])
    
    print("\n测试文本:", test_text)
    print("输出结果:")
    
    async for result in buffer.generator():
        print(f"  {result.type_.value}: '{result.content}'")

if __name__ == "__main__":
    asyncio.run(test_citation_stream())
    asyncio.run(test_bracket_only())
    asyncio.run(test_incomplete_citation())
