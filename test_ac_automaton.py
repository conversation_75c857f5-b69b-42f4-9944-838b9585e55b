import asyncio
from collections import deque
from enum import Enum

class BufferType(Enum):
    SENSITIVE = "sensitive"
    CORNER = "corner"
    CONTENT = "content"

class Buffer:
    def __init__(self, content: str, type_: BufferType):
        self.content = content
        self.type_ = type_
    
    def __repr__(self):
        return f"Buffer(content='{self.content}', type_={self.type_.value})"

class TrieNode:
    def __init__(self):
        self.children = {}
        self.fail = None
        self.output = []

class ACAutomaton:
    def __init__(self):
        self.root = TrieNode()

    def add_word(self, pattern):
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.output.append(pattern)

    def build_failure_pointers(self):
        queue = deque()
        
        for char, node in self.root.children.items():
            node.fail = self.root
            queue.append(node)

        while queue:
            current_node = queue.popleft()
            
            for char, next_node in current_node.children.items():
                fail_node = current_node.fail
                while fail_node is not None and char not in fail_node.children:
                    fail_node = fail_node.fail
                if fail_node is None:
                    next_node.fail = self.root
                else:
                    next_node.fail = fail_node.children[char]
                    next_node.output.extend(next_node.fail.output)
                queue.append(next_node)

class TestQABuffer:
    def __init__(self):
        self.buffer_deque = deque()
        self.citation_content = {"[", "citation", ":"}
        self.citation_end = {"]"}
        
        self.ac = ACAutomaton()
        for keyword in self.citation_content | self.citation_end:
            self.ac.add_word(keyword)
        self.ac.build_failure_pointers()

        print("AC自动机构建完成:")
        print(f"  Root children: {list(self.ac.root.children.keys())}")
        for char, node in self.ac.root.children.items():
            print(f"  '{char}' -> output: {node.output}, children: {list(node.children.keys())}")

    async def generator(self, stream):
        node = self.ac.root

        async for char in stream:
            print(f"处理字符: '{char}', 当前缓冲区: {''.join(self.buffer_deque)}")
            
            # AC自动机状态转移
            while node is not None and char not in node.children:
                node = node.fail

            if node is None:
                node = self.ac.root
                print(f"  重置到root，重新尝试匹配字符 '{char}'")
                # 重新尝试从root开始匹配当前字符
                if char in node.children:
                    node = node.children[char]
                    self.buffer_deque.append(char)
                    print(f"  从root匹配成功，node.output: {node.output}")
                    # 检查是否有匹配输出，如果有则继续到下面的匹配处理逻辑
                    if not node.output:
                        continue
                else:
                    # 当前字符无法匹配，处理缓冲区和当前字符
                    if char in self.citation_content:
                        self.buffer_deque.append(char)
                        print(f"  字符在citation_content中，添加到缓冲区")
                        continue
                    # 检查缓冲区开头是否为引用标记
                    if char in self.citation_end and "".join(list(self.buffer_deque))[:12].replace(" ", "").startswith("[citation:"):
                        self.buffer_deque.append(char)
                        print(f"  字符是citation_end且缓冲区匹配，添加到缓冲区")
                    else:
                        # 输出缓冲区内容（如果有）
                        while self.buffer_deque:
                            char_to_output = self.buffer_deque.popleft()
                            print(f"  输出缓冲区字符: '{char_to_output}'")
                            yield Buffer(content=char_to_output, type_=BufferType.CONTENT)
                        # 输出当前字符
                        print(f"  输出当前字符: '{char}'")
                        yield Buffer(content=char, type_=BufferType.CONTENT)
                    continue
            else:
                node = node.children.get(char)

            # 判断匹配到了哪个模式
            if node and node.output:
                print(f"  匹配到模式: {node.output}")
                self.buffer_deque.append(char)
            else:
                self.buffer_deque.append(char)

        # 处理剩余缓冲区内容
        while self.buffer_deque:
            char_to_output = self.buffer_deque.popleft()
            print(f"最终输出缓冲区字符: '{char_to_output}'")
            yield Buffer(content=char_to_output, type_=BufferType.CONTENT)

async def test_bracket_matching():
    """测试'['字符的匹配"""
    test_text = "[citation:1]"
    
    async def char_stream():
        for char in test_text:
            yield char
    
    buffer = TestQABuffer()
    
    print(f"测试文本: {test_text}")
    print("=" * 50)
    
    results = []
    async for result in buffer.generator(char_stream()):
        results.append(result)
        print(f"输出: {result}")
    
    print("=" * 50)
    print("最终结果:", "".join([r.content for r in results]))

if __name__ == "__main__":
    asyncio.run(test_bracket_matching())
